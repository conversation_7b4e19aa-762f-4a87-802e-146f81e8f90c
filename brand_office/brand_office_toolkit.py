#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Brand Office Address Update Toolkit
Công cụ cập nhật địa chỉ brand_office với nhiều tùy chọn
"""

import os
import sys
import subprocess
from pathlib import Path

class BrandOfficeToolkit:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.exports_dir = self.base_dir / "exports"
        
    def check_requirements(self):
        """Kiểm tra và cài đặt requirements"""
        print("🔍 Kiểm tra requirements...")
        
        try:
            import pandas
            import geopandas
            import shapely
            import mysql.connector
            print("✅ Tất cả thư viện đã được cài đặt")
            return True
        except ImportError as e:
            print(f"❌ Thiếu thư viện: {e}")
            print("📦 Đang cài đặt requirements...")
            
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", "-r", 
                    str(self.base_dir / "requirements.txt")
                ])
                print("✅ Đã cài đặt thành công requirements")
                return True
            except subprocess.CalledProcessError:
                print("❌ Lỗi cài đặt requirements")
                return False
    
    def setup_directories(self):
        """Tạo thư mục cần thiết"""
        print("📁 Thiết lập thư mục...")
        self.exports_dir.mkdir(exist_ok=True)
        print(f"✅ Đã tạo thư mục: {self.exports_dir}")
    
    def run_address_update(self):
        """Chạy cập nhật địa chỉ"""
        print("🚀 Bắt đầu cập nhật địa chỉ brand_office...")
        
        try:
            from update_brand_office_address import BrandOfficeAddressUpdater
            
            updater = BrandOfficeAddressUpdater()
            updater.run()
            
        except Exception as e:
            print(f"❌ Lỗi chạy update: {e}")
    
    def view_results(self):
        """Xem kết quả"""
        csv_file = self.exports_dir / "brand_office_updated.csv"
        
        if csv_file.exists():
            print(f"📊 File kết quả: {csv_file}")
            
            try:
                import pandas as pd
                df = pd.read_csv(csv_file)
                
                print(f"\n📈 THỐNG KÊ:")
                print(f"- Tổng records: {len(df)}")
                print(f"- Matched: {len(df[df['status'] == 'matched'])}")
                print(f"- Unmatched: {len(df[df['status'] == 'unmatched'])}")
                
                print(f"\n📋 5 RECORDS ĐẦU TIÊN:")
                print(df.head().to_string())
                
            except Exception as e:
                print(f"❌ Lỗi đọc file: {e}")
        else:
            print("❌ Chưa có file kết quả")
    
    def analyze_unmatched(self):
        """Phân tích records không match"""
        csv_file = self.exports_dir / "brand_office_updated.csv"
        
        if not csv_file.exists():
            print("❌ Chưa có file kết quả")
            return
            
        try:
            import pandas as pd
            df = pd.read_csv(csv_file)
            
            unmatched = df[df['status'] == 'unmatched']
            
            if len(unmatched) == 0:
                print("✅ Tất cả records đều đã match")
                return
            
            print(f"📊 PHÂN TÍCH {len(unmatched)} RECORDS UNMATCHED:")
            
            # Group theo province_code
            province_stats = unmatched.groupby('province_code').size().sort_values(ascending=False)
            print(f"\n📍 Theo tỉnh:")
            for province_code, count in province_stats.head(10).items():
                print(f"  - Province {province_code}: {count} records")
            
            # Lưu unmatched records
            unmatched_file = self.exports_dir / "unmatched_records.csv"
            unmatched.to_csv(unmatched_file, index=False)
            print(f"\n💾 Đã lưu unmatched records vào: {unmatched_file}")
            
        except Exception as e:
            print(f"❌ Lỗi phân tích: {e}")
    
    def show_menu(self):
        """Hiển thị menu"""
        print("\n" + "="*60)
        print("🏢 BRAND OFFICE ADDRESS UPDATE TOOLKIT")
        print("="*60)
        print("1. Kiểm tra và cài đặt requirements")
        print("2. Thiết lập thư mục")
        print("3. Chạy cập nhật địa chỉ")
        print("4. Xem kết quả")
        print("5. Phân tích records không match")
        print("6. Chạy toàn bộ (1+2+3)")
        print("0. Thoát")
        print("="*60)
    
    def run(self):
        """Chạy toolkit"""
        while True:
            self.show_menu()
            
            try:
                choice = input("\n👉 Chọn tùy chọn (0-6): ").strip()
                
                if choice == "0":
                    print("👋 Tạm biệt!")
                    break
                elif choice == "1":
                    self.check_requirements()
                elif choice == "2":
                    self.setup_directories()
                elif choice == "3":
                    self.run_address_update()
                elif choice == "4":
                    self.view_results()
                elif choice == "5":
                    self.analyze_unmatched()
                elif choice == "6":
                    print("🚀 Chạy toàn bộ process...")
                    if self.check_requirements():
                        self.setup_directories()
                        self.run_address_update()
                        self.view_results()
                else:
                    print("❌ Tùy chọn không hợp lệ")
                    
                input("\n⏸️  Nhấn Enter để tiếp tục...")
                
            except KeyboardInterrupt:
                print("\n👋 Tạm biệt!")
                break
            except Exception as e:
                print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    toolkit = BrandOfficeToolkit()
    toolkit.run()
