#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script để kiểm tra import gemini
"""

print("🔍 Testing imports step by step...")

try:
    print("1. Testing google.generativeai import...")
    import google.generativeai as genai
    print("✅ google.generativeai import OK")
except Exception as e:
    print(f"❌ google.generativeai import failed: {e}")
    exit(1)

try:
    print("2. Testing mysql.connector import...")
    import mysql.connector
    print("✅ mysql.connector import OK")
except Exception as e:
    print(f"❌ mysql.connector import failed: {e}")

try:
    print("3. Testing other imports...")
    import pandas as pd
    import asyncio
    import os
    from tqdm.asyncio import tqdm
    import logging
    from mysql.connector import Error
    from mysql.connector import connect
    print("✅ All other imports OK")
except Exception as e:
    print(f"❌ Other imports failed: {e}")

try:
    print("4. Testing Gemini class import...")
    from gemini import Gemini
    print("✅ Gemini class import OK")
except Exception as e:
    print(f"❌ Gemini class import failed: {e}")
    exit(1)

try:
    print("5. Testing Gemini class instantiation...")
    # Test với mock API key để tránh lỗi network
    test_gemini = Gemini()
    print("✅ Gemini class instantiation OK")
    print(f"   - Model: {test_gemini.model}")
    print(f"   - Chat: {test_gemini.chat}")
except Exception as e:
    print(f"❌ Gemini class instantiation failed: {e}")
    print("   This might be due to API key or network issues")

try:
    print("6. Testing gemini instance import...")
    from gemini import gemini
    print("✅ gemini instance import OK")
    print(f"   - Type: {type(gemini)}")
except Exception as e:
    print(f"❌ gemini instance import failed: {e}")

print("\n🎉 Import test completed!")
