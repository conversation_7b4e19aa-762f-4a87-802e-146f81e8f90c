# Brand Office Address Update Tool

Công cụ cập nhật địa chỉ brand_office dựa trên dữ liệu geometry từ Task 4.

## Mục tiêu

Sử dụng dữ liệu geometry để xác định chính xác xã/phường và tỉnh/thành phố cho từng brand_office dựa trên tọa độ lat/long, sau đó cập nhật lại cột address với định dạng mới.

## Định dạng địa chỉ

- **Hiện tại:** "<PERSON><PERSON>h<PERSON>, <PERSON><PERSON><PERSON> đ<PERSON> (Khu dân cư), Xã/Phường, Quận/Huyện, Tỉnh/Thành phố"
- **Mới:** "<PERSON><PERSON> nhà, Tên đư<PERSON> (Khu dân cư), Xã/Phường, Tỉnh/Thành phố" (bỏ Quận/Huyện)

## Cài đặt

1. **Cài đặt dependencies:**
```bash
pip install -r brand_office/requirements.txt
```

2. **Hoặc sử dụng toolkit tự động:**
```bash
cd brand_office
python brand_office_toolkit.py
```

## Sử dụng

### Cách 1: Sử dụng Toolkit (Khuyến nghị)

```bash
cd brand_office
python brand_office_toolkit.py
```

Toolkit cung cấp menu với các tùy chọn:
- Kiểm tra và cài đặt requirements
- Thiết lập thư mục
- Chạy cập nhật địa chỉ
- Xem kết quả
- Phân tích records không match
- Chạy toàn bộ process

### Cách 2: Chạy trực tiếp

```bash
cd brand_office
python update_brand_office_address.py
```

## Kết quả

Tool sẽ tạo file CSV trong `brand_office/exports/`:
- `brand_office_updated.csv`: Kết quả chính
- `unmatched_records.csv`: Records không tìm được ward
- `update_address.log`: Log chi tiết

## Cấu trúc dữ liệu output

CSV chứa các cột:
- `id`: ID brand_office
- `lat`, `long`: Tọa độ
- `city_id`: ID tỉnh cũ
- `address_old`: Địa chỉ cũ
- `province_code`: Mã tỉnh mới
- `ward_code`: Mã xã/phường
- `ward_title`: Tên xã/phường
- `province_title`: Tên tỉnh
- `new_address`: Địa chỉ mới
- `status`: matched/unmatched

## Xử lý lỗi

Tool có các cơ chế xử lý lỗi:
- Retry cho geometry parsing
- Log chi tiết các trường hợp không match
- Xử lý exception cho dữ liệu không hợp lệ
- Batch processing để tránh memory overflow

## Database

Tool kết nối đến database urbox với config:
- Host: 127.0.0.1:3306
- Database: urbox
- User: root/root

## Lưu ý kỹ thuật

- Sử dụng GeoPandas cho geometry operations
- Point-in-polygon checking với contains() và intersects()
- Batch processing 1000 records/lần
- UTF-8 encoding cho CSV output
- Comprehensive logging

## Troubleshooting

### Lỗi cài đặt geopandas
```bash
# macOS
brew install gdal
pip install geopandas

# Ubuntu
sudo apt-get install gdal-bin libgdal-dev
pip install geopandas
```

### Lỗi kết nối database
- Kiểm tra MySQL đang chạy
- Kiểm tra thông tin kết nối
- Kiểm tra quyền truy cập

### Memory issues
- Giảm batch_size trong code
- Tăng RAM hoặc swap space
