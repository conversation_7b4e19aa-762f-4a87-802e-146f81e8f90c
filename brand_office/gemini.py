import google.generativeai as genai
import logging

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exports/brand_office_gemini.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Gemini:
    def __init__(self, model = 'gemini-2.0-flash-001'):
        genai.configure(api_key='AIzaSyCwxwd_feOQJuS5HfB9kZqcJ7Nv1zylVMA')
        self.model = genai.GenerativeModel(model)
        self.chat = self.model.start_chat(history=[])

    def build_conversion_prompt(self):
        """Xây dựng một prompt chi tiết cho Gemini để đảm bảo kết quả chính xác.
    Đ<PERSON>y là "bộ não" của tác vụ.
        """
        # Sử dụng kỹ thuật "few-shot prompting" bằng cách đưa ra vài ví dụ
        # để AI hiểu rõ định dạng và yêu cầu.
        prompt = f"""
Bạn là một chuyên gia xử lý và chuẩn hóa địa chỉ tại Việt Nam.
Nhiệm vụ của bạn là nhận một địa chỉ cũ và thông tin xã/tỉnh mới, sau đó tạo ra một địa chỉ mới theo các quy tắc nghiêm ngặt sau:

1.  **Giữ nguyên** các chi tiết của địa chỉ cũ như: số nhà, tên đường, ngõ, ngách, hẻm, thôn, xóm, ấp.
2.  **Xóa bỏ hoàn toàn** thông tin về Quận/Huyện/Thị xã cũ.
3.  **Thay thế** hoặc **bổ sung** thông tin Xã/Phường bằng "Xã/Phường mới" được cung cấp.
4.  **Thay thế** hoặc **bổ sung** thông tin Tỉnh/Thành phố bằng "Tỉnh/Thành phố mới" được cung cấp.
5.  **Chỉ trả về chuỗi địa chỉ mới**, không thêm bất kỳ lời giải thích hay câu chữ thừa nào.

---
**Ví dụ 1:**
- Địa chỉ cũ: Thôn Đoài, xã Minh Trí, huyện Sóc Sơn, Thành phố Hà Nội
- Xã/Phường mới: Xã Tân Minh
- Tỉnh/Thành phố mới: Thành phố Hà Nội
- KẾT QUẢ ĐÚNG: Thôn Đoài, Xã Tân Minh, Thành phố Hà Nội

**Ví dụ 2:**
- Địa chỉ cũ: Số 25, phố Tràng Tiền, quận Hoàn Kiếm, Hà Nội
- Xã/Phường mới: Phường Tràng Tiền
- Tỉnh/Thành phố mới: Thành phố Hà Nội
- KẾT QUẢ ĐÚNG: Số 25, phố Tràng Tiền, Phường Tràng Tiền, Thành phố Hà Nội

**Ví dụ 3:**
- Địa chỉ cũ: Thôn 5, xã Ea M'nang, huyện Cư M'gar, tỉnh Đắk Lắk
- Xã/Phường mới: Xã Dliêya
- Tỉnh/Thành phố mới: Tỉnh Đắk Nông
- KẾT QUẢ ĐÚNG: Thôn 5, Xã Dliêya, Tỉnh Đắk Nông
---

**Sau tin nhắn này, tôi sẽ bắt đầu gửi dữ liệu. Hãy sẵn sàng.**
"""
        return prompt
    def start_chat(self):
        self.chat = self.model.start_chat(history=[])
        # 1. GỬI PROMPT HỆ THỐNG (DÀI) CHỈ MỘT LẦN
        print("Đang gửi hướng dẫn ban đầu cho Gemini...")
        initial_prompt = self.build_conversion_prompt()
        # Chúng ta không cần phản hồi của tin nhắn này, chỉ cần "dạy" cho AI
        return initial_prompt
    
    
    async def convert_address_async(self,initial_prompt, brand_office, ward, province):
        """
        Hàm bất đồng bộ để gọi API cho một địa chỉ duy nhất.
        """
        
        # Bắt đầu một phiên trò chuyện    
        await self.chat.send_message_async(initial_prompt)        
        short_message = self.format_subsequent_message(
            brand_office['address_old'],
            ward,
            province
        )
        try:
            # Gửi tin nhắn ngắn và chờ phản hồi
            response = await self.chat.send_message_async(short_message)
            return response.text.strip()
        except Exception as e:
            print(f"Lỗi khi xử lý dòng {brand_office['id']}: {e}")
            return "CONVERSION_ERROR"
            # Cân nhắc: Có thể khởi tạo lại chat session nếu lỗi nghiêm trọng
            # chat = model.start_chat(history=[]) 
            # await chat.send_message_async(get_initial_system_prompt())
    
    def format_subsequent_message(self,old_address, new_commune, new_province):
        """
        Định dạng tin nhắn ngắn gọn cho mỗi địa chỉ sau tin nhắn đầu tiên.
        """
        return f"""
    - Địa chỉ cũ: "{old_address}"
    - Xã/Phường mới: "{new_commune}"
    - Tỉnh/Thành phố mới: "{new_province}"
    """
  
# Khởi tạo instance gemini
# Chỉ khởi tạo khi được import, không phải khi chạy trực tiếp
if __name__ != "__main__":
    try:
        gemini = Gemini()
        logger.info("✅ Gemini instance đã được khởi tạo thành công")
    except Exception as e:
        logger.error(f"❌ Lỗi khởi tạo Gemini instance: {e}")
        gemini = None
