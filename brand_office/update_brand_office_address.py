#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cập nhật địa chỉ brand_office dựa trên geometry data
Task: Sử dụng dữ liệu geometry để xác định chính xác xã/phường và tỉnh/thành phố
"""

import json
import logging
import pandas as pd
import mysql.connector
from mysql.connector import Error
from shapely.geometry import Point, shape
import warnings

from gemini import gemini
warnings.filterwarnings('ignore')
from tqdm.asyncio import tqdm
from vnaddress import VNAddressStandardizer
import asyncio


# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exports/update_address.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BrandOfficeAddressUpdater:
    def __init__(self):
        self.connection = None
        self.results = []
        self.batch_size = 1000
        self.processed_count = 0
        self.matched_count = 0
        self.unmatched_count = 0
        self.gemini = gemini
        
        
    def get_database_connection(self):
        """Tạo kết nối database"""
        try:
            connection = mysql.connector.connect(
                host='127.0.0.1',
                port=3306,
                database='urbox',
                user='root',
                password='root',
                charset='utf8mb4'
            )
            
            if connection.is_connected():
                logger.info("✅ Kết nối database thành công!")
                return connection
                
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return None
    
    def get_brand_office_data(self, offset=0, limit=1000):
        """Lấy dữ liệu brand_office theo batch"""
        query = """
        SELECT id, latitude, longitude, city_id, address_old
        FROM brand_office
        WHERE address_old IS NOT NULL
        AND address_old != ''
        AND latitude IS NOT NULL
        AND longitude IS NOT NULL
        AND city_id > 0
        LIMIT %s OFFSET %s
        """
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, (limit, offset))
            results = cursor.fetchall()
            cursor.close()
            
            logger.info(f"📊 Lấy được {len(results)} records brand_office (offset: {offset})")
            return results
            
        except Error as e:
            logger.error(f"❌ Lỗi lấy dữ liệu brand_office: {e}")
            return []
    
    def get_province_mapping(self):
        """Lấy mapping province"""
        query = """
        SELECT id, new_pti_id
        FROM ___province
        WHERE is_new = 1
        """
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query)
            results = cursor.fetchall()
            cursor.close()
            
            # Tạo dictionary mapping
            mapping = {row['id']: row['new_pti_id'] for row in results}
            logger.info(f"📊 Lấy được {len(mapping)} province mappings")
            return mapping
            
        except Error as e:
            logger.error(f"❌ Lỗi lấy province mapping: {e}")
            return {}
    
    def get_geo_ward_data(self):
        """Lấy dữ liệu geo_ward"""
        query = """
        SELECT geometry, geo_province_code, province_title, ward_title, code
        FROM geo_ward
        WHERE geometry IS NOT NULL
        """
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query)
            results = cursor.fetchall()
            cursor.close()
            
            logger.info(f"📊 Lấy được {len(results)} geo_ward records")
            return results
            
        except Error as e:
            logger.error(f"❌ Lỗi lấy geo_ward data: {e}")
            return []
    
    def parse_geometry(self, geometry_str):
        """Parse geometry từ JSON string thành Shapely object"""
        try:
            if isinstance(geometry_str, str):
                geometry_data = json.loads(geometry_str)
            else:
                geometry_data = geometry_str
                
            return shape(geometry_data)
        except Exception as e:
            logger.warning(f"⚠️ Lỗi parse geometry: {e}")
            return None
    
    def find_ward_by_coordinates(self, lat, lng, province_code, geo_ward_data):
        """Tìm ward chứa tọa độ"""
        try:
            point = Point(lng, lat)
            
            # Filter theo province code
            filtered_wards = [
                ward for ward in geo_ward_data 
                if int(ward['geo_province_code']) == int(province_code)
            ]
            
            for ward in filtered_wards:
                geometry = self.parse_geometry(ward['geometry'])
                if geometry:
                    # Thử contains() trước (chính xác hơn)
                    if geometry.contains(point):
                        return ward
                    # Fallback: intersects() (cho trường hợp point ở biên)
                    elif geometry.intersects(point):
                        return ward
                    # Fallback: buffer cho trường hợp point ở gần đó
                    elif geometry.buffer(0.001).intersects(point):
                        return ward
            return None
            
        except Exception as e:
            logger.warning(f"⚠️ Lỗi tìm ward cho tọa độ ({lat}, {lng}): {e}")
            return None
    
    def create_new_address(self, street_info, ward_title, province_title):
        """Tạo địa chỉ mới theo định dạng yêu cầu"""
        return f"{street_info}, {ward_title}, {province_title}"
    
    async def process_batch(self, brand_office_data, province_mapping, geo_ward_data, initial_prompt):
        """Xử lý một batch dữ liệu"""
        batch_results = []
        tasks = []
        for record in tqdm(brand_office_data, total=len(brand_office_data), desc="Đang xử lý địa chỉ"):
            try:
                # Map city_id với province
                city_id = record['city_id']
                province_code = province_mapping.get(city_id)
                if not province_code:
                    logger.warning(f"⚠️ Không tìm được province mapping cho city_id: {city_id}")
                    continue
                # Tìm ward chứa tọa độ
                ward = self.find_ward_by_coordinates(
                    record['latitude'], record['longitude'], 
                    province_code, geo_ward_data
                )
                if ward:
                    # Tạo task xử lý bất đồng bộ
                    tasks.append(self._process_record_async(record, ward, province_code, initial_prompt))
                else:
                    # Không tìm được ward
                    result = {
                        'id': record['id'],
                        'latitude': record['latitude'],
                        'longitude': record['longitude'],
                        'city_id': record['city_id'],
                        'geo_province_code': None,
                        'geo_ward_code': None,
                        'address_old': record['address_old'],
                        'province_code': province_code,
                        'ward_code': None,
                        'ward_title': None,
                        'province_title': None,
                        'new_address': None,
                        'status': 'unmatched'
                    }
                    batch_results.append(result)
                    self.unmatched_count += 1
                self.processed_count += 1
                if self.processed_count % 100 == 0:
                    logger.info(f"📊 Đã xử lý {self.processed_count} records")
            except Exception as e:
                logger.error(f"❌ Lỗi xử lý record ID {record['id']}: {e}")
                continue
        # Chờ tất cả các task async hoàn thành
        if tasks:
            batch_results.extend(await asyncio.gather(*tasks))
        return batch_results

    async def _process_record_async(self, record, ward, province_code, initial_prompt):
        try:
            new_address = await self.gemini.convert_address_async(initial_prompt, record, ward['ward_title'], ward['province_title'])
            result = {
                'id': record['id'],
                'latitude': record['latitude'],
                'longitude': record['longitude'],
                'city_id': record['city_id'],
                'geo_province_code': ward['geo_province_code'],
                'geo_ward_code': ward['code'],
                'address_old': record['address_old'],
                'province_code': province_code,
                'ward_code': ward['code'],
                'ward_title': ward['ward_title'],
                'province_title': ward['province_title'],
                'new_address': new_address,
                'status': 'matched'
            }
            self.matched_count += 1
            return result
        except Exception as e:
            logger.error(f"❌ Lỗi xử lý record ID {record['id']}: {e}")
            return {
                'id': record['id'],
                'latitude': record['latitude'],
                'longitude': record['longitude'],
                'city_id': record['city_id'],
                'geo_province_code': ward['geo_province_code'],
                'geo_ward_code': ward['code'],
                'address_old': record['address_old'],
                'province_code': province_code,
                'ward_code': ward['code'],
                'ward_title': ward['ward_title'],
                'province_title': ward['province_title'],
                'new_address': None,
                'status': 'error'
            }

    def save_results_to_csv(self, results, filename='brand_office_updated.csv'):
        """Lưu kết quả ra CSV"""
        try:
            df = pd.DataFrame(results)
            filepath = f"exports/{filename}"
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            logger.info(f"✅ Đã lưu {len(results)} records vào {filepath}")
            
            # Thống kê
            matched = len([r for r in results if r['status'] == 'matched'])
            unmatched = len([r for r in results if r['status'] == 'unmatched'])
            
            logger.info(f"📊 THỐNG KÊ: Matched: {matched}, Unmatched: {unmatched}")
            
        except Exception as e:
            logger.error(f"❌ Lỗi lưu CSV: {e}")
    
    async def run(self):
        """Chạy toàn bộ process"""
        try:
            logger.info("🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE")
            # Kết nối database
            self.connection = self.get_database_connection()
            if not self.connection:
                return
            # Lấy dữ liệu mapping và geo_ward
            logger.info("📊 Lấy dữ liệu mapping và geometry...")
            province_mapping = self.get_province_mapping()
            geo_ward_data = self.get_geo_ward_data()
            if not province_mapping or not geo_ward_data:
                logger.error("❌ Không có dữ liệu mapping hoặc geo_ward")
                return
            # Xử lý theo batch
            offset = 0
            all_results = []
            initial_prompt = self.gemini.start_chat()
            while True:
                brand_office_data = self.get_brand_office_data(offset, self.batch_size)
                if not brand_office_data:
                    break
                logger.info(f"🔄 Xử lý batch {offset//self.batch_size + 1}")
                batch_results = await self.process_batch(brand_office_data, province_mapping, geo_ward_data, initial_prompt)
                all_results.extend(batch_results)
                offset += self.batch_size
            # Lưu kết quả
            if all_results:
                self.save_results_to_csv(all_results)
            logger.info(f"✅ HOÀN THÀNH! Tổng: {self.processed_count}, Matched: {self.matched_count}, Unmatched: {self.unmatched_count}")
        except Exception as e:
            logger.error(f"❌ Lỗi trong quá trình xử lý: {e}")
        finally:
            if self.connection:
                self.connection.close()

if __name__ == "__main__":
    updater = BrandOfficeAddressUpdater()
    asyncio.run(updater.run())
