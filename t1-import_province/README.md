# Import Dữ Liệu Tỉnh/Thành và Xã/Phường

Script để import dữ liệu từ file CSV vào database urbox.

## Mục <PERSON>

- [Tổng Quan](#tổng-quan)
- [Cấu Trúc File](#cấu-trúc-file)
- [Cài Đặt](#cài-đặt)
- [Hướng Dẫn Sử Dụng](#hướng-dẫn-sử-dụng)
- [Quy Trình Import](#quy-trình-import)
- [Validation](#validation)
- [Troubleshooting](#troubleshooting)

## Tổng Quan

Dự án này cung cấp các script để import dữ liệu từ file CSV vào database urbox, cụ thể:

- Import dữ liệu tỉnh/thành từ `province.csv` vào bảng `___province`
- Import dữ liệu xã/phường từ `ward_new.csv` vào bảng `ward`
- Xử lý dữ liệu và tạo các trường bổ sung:
  - `is_city`: <PERSON><PERSON><PERSON> đ<PERSON> là tỉnh (0) hay thành phố (1)
  - `safe_title`: Slug tiếng Việt không dấu
  - `title_full`: Tên đầy đủ của xã/phường

## Cấu Trúc File

```
.
├── province.csv                # File CSV chứa dữ liệu tỉnh/thành
├── ward_new.csv                # File CSV chứa dữ liệu xã/phường
├── import_csv_to_database.py   # Script Python để xử lý và tạo SQL
├── import_province_data.sql    # Script SQL để import dữ liệu tỉnh/thành
├── import_ward_data.sql        # Script SQL để import dữ liệu xã/phường
└── README.md                   # Tài liệu hướng dẫn
```

## Cài Đặt

### Yêu Cầu

- Python 3.6+
- MySQL/MariaDB
- Thư viện Python: `mysql-connector-python`

### Cài Đặt Thư Viện

```bash
pip install mysql-connector-python
```

## Hướng Dẫn Sử Dụng


1. Đặt file CSV vào cùng thư mục với script Python:
   - `province.csv`
   - `ward_new.csv`

2. Chạy script Python để tạo các file SQL:

```bash
python import_csv_to_database.py
```

3. Script sẽ tạo ra 3 file:
   - `insert_provinces.sql`: SQL để import dữ liệu tỉnh/thành
   - `insert_wards.sql`: SQL để import dữ liệu xã/phường
   - `validation_import.sql`: SQL để kiểm tra dữ liệu sau khi import

4. Import dữ liệu vào database:

```bash
mysql -u username -p urbox < insert_provinces.sql
mysql -u username -p urbox < insert_wards.sql
```

5. Kiểm tra dữ liệu:

```bash
mysql -u username -p urbox < validation_import.sql
```

## Quy Trình Import

### Import Tỉnh/Thành

1. Tạo bảng tạm để import dữ liệu từ CSV
2. Import dữ liệu từ CSV vào bảng tạm
3. Xử lý dữ liệu:
   - Xác định `is_city` dựa trên title
   - Tạo `safe_title` bằng cách bỏ "Tỉnh"/"Thành phố" và chuyển thành slug không dấu
4. Insert dữ liệu vào bảng `___province`
5. Kiểm tra dữ liệu đã import

### Import Xã/Phường

1. Tạo bảng tạm để import dữ liệu từ CSV
2. Import dữ liệu từ CSV vào bảng tạm
3. Xử lý dữ liệu:
   - Mapping `province_pti_id` với `province_id` từ bảng `___province`
   - Tạo `title_full` bằng cách gộp `prefix`, `title` và `province_title`
4. Insert dữ liệu vào bảng `ward`
5. Kiểm tra dữ liệu đã import

## Validation

Sau khi import, cần kiểm tra:

1. Số lượng records:
   - 34 tỉnh/thành
   - 3,321 xã/phường

2. Mapping giữa province và ward:
   - Mỗi province phải có ít nhất 1 ward
   - Tổng số ward phải đúng 3,321

3. Dữ liệu đặc biệt:
   - `is_city`: 5 thành phố, 29 tỉnh
   - `safe_title`: Đúng format slug không dấu
   - `title_full`: Đúng format "Prefix Title, Province Title"

## Troubleshooting

### Lỗi Import CSV

Nếu gặp lỗi khi import CSV, kiểm tra:
- Encoding của file CSV (nên là UTF-8)
- Quyền truy cập file của MySQL
- Đường dẫn file trong script

### Lỗi Mapping

Nếu gặp lỗi mapping giữa province và ward:
1. Kiểm tra dữ liệu trong bảng `___province` đã import thành công chưa
2. Kiểm tra `pti_id` trong cả hai bảng có khớp nhau không

### Lỗi Tiếng Việt

Nếu gặp lỗi hiển thị tiếng Việt:
- Kiểm tra charset của database và bảng (nên là utf8mb4)
- Kiểm tra collation (nên là utf8mb4_unicode_ci hoặc utf8mb4_general_ci)
