-- Validation dữ liệu sau khi import
-- Tạo: 2025-07-21 18:02:55

USE urbox;

-- <PERSON><PERSON><PERSON> tra số lượng provinces
SELECT 'PROVINCE COUNT' AS info, COUNT(*) AS count 
FROM ___province WHERE is_new = 2;

-- <PERSON><PERSON><PERSON> tra số lượng wards
SELECT 'WARD COUNT' AS info, COUNT(*) AS count 
FROM ward WHERE is_new = 2;

-- <PERSON><PERSON><PERSON> tra is_city distribution
SELECT 
    'CITY DISTRIBUTION' AS info,
    SUM(CASE WHEN is_city = 1 THEN 1 ELSE 0 END) AS cities,
    SUM(CASE WHEN is_city = 0 THEN 1 ELSE 0 END) AS provinces
FROM ___province WHERE is_new = 2;

-- <PERSON><PERSON><PERSON> tra ward count theo province
SELECT 
    p.id AS province_id,
    p.title AS province_title,
    COUNT(w.id) AS ward_count
FROM ___province p
LEFT JOIN ward w ON w.province_id = p.id AND w.is_new = 2
WHERE p.is_new = 2
GROUP BY p.id, p.title
ORDER BY p.position;

-- <PERSON><PERSON><PERSON> tra dữ liệu mẫu
SELECT 'SAMPLE PROVINCES' AS info;
SELECT id, pti_id, title, safe_title, is_city, position 
FROM ___province WHERE is_new = 2 ORDER BY position LIMIT 5;

SELECT 'SAMPLE WARDS' AS info;
SELECT id, pti_id, province_id, prefix, title, title_full 
FROM ward WHERE is_new = 2 ORDER BY province_id, id LIMIT 5;
